from fastapi import APIRouter, Depends, HTTPException, status, Form, File, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, update
from sqlalchemy.orm import selectinload
from typing import List, Optional
from database.database import get_db
from models.gallery_timeline import GalleryTimelineEntry, gallery_timeline_images
from models.image import Image
from schemas.schemas import (
    GalleryTimelineEntryCreate, 
    GalleryTimelineEntryUpdate, 
    GalleryTimelineEntryResponse
)
from utils.auth import get_current_user

router = APIRouter(prefix="/gallery", tags=["gallery-timeline"])

@router.get("/timeline", response_model=List[GalleryTimelineEntryResponse])
async def get_timeline_entries(
    skip: int = 0,
    limit: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """获取Gallery时间线条目列表"""
    result = await db.execute(
        select(GalleryTimelineEntry)
        .options(selectinload(GalleryTimelineEntry.images))
        .order_by(GalleryTimelineEntry.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    entries = result.scalars().all()
    return entries

@router.get("/timeline/{entry_id}", response_model=GalleryTimelineEntryResponse)
async def get_timeline_entry(
    entry_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取单个Gallery时间线条目"""
    result = await db.execute(
        select(GalleryTimelineEntry)
        .options(selectinload(GalleryTimelineEntry.images))
        .where(GalleryTimelineEntry.id == entry_id)
    )
    entry = result.scalars().first()
    
    if not entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Timeline entry not found"
        )
    
    return entry

@router.post("/timeline", response_model=GalleryTimelineEntryResponse)
async def create_timeline_entry(
    content: str = Form(...),
    title: Optional[str] = Form(None),
    location: Optional[str] = Form(None),
    is_active: bool = Form(True),
    is_featured: bool = Form(False),
    show_on_homepage: bool = Form(False),
    image_ids: Optional[List[str]] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建Gallery时间线条目"""
    # 验证内容不能为空
    if not content or content.strip() == "":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="时间线内容不能为空"
        )

    # 创建时间线条目
    db_entry = GalleryTimelineEntry(
        title=title,
        content=content.strip(),
        location=location,
        is_active=is_active,
        is_featured=is_featured,
        show_on_homepage=show_on_homepage
    )
    
    db.add(db_entry)
    await db.commit()
    await db.refresh(db_entry)
    
    # 关联图片
    if image_ids:
        for image_id_str in image_ids:
            try:
                image_id = int(image_id_str)
                # 检查图片是否存在
                result = await db.execute(select(Image).where(Image.id == image_id))
                image = result.scalars().first()
                if image:
                    # 插入关联关系
                    await db.execute(
                        gallery_timeline_images.insert().values(
                            timeline_entry_id=db_entry.id,
                            image_id=image_id,
                            sort_order=0
                        )
                    )
            except ValueError:
                continue
    
    await db.commit()
    
    # 重新获取包含图片的条目
    result = await db.execute(
        select(GalleryTimelineEntry)
        .options(selectinload(GalleryTimelineEntry.images))
        .where(GalleryTimelineEntry.id == db_entry.id)
    )
    entry = result.scalars().first()
    
    return entry

@router.put("/timeline/{entry_id}", response_model=GalleryTimelineEntryResponse)
async def update_timeline_entry(
    entry_id: int,
    content: Optional[str] = Form(None),
    title: Optional[str] = Form(None),
    location: Optional[str] = Form(None),
    is_active: Optional[bool] = Form(None),
    is_featured: Optional[bool] = Form(None),
    show_on_homepage: Optional[bool] = Form(None),
    image_ids: Optional[List[str]] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """更新Gallery时间线条目"""
    # 获取条目
    result = await db.execute(
        select(GalleryTimelineEntry).where(GalleryTimelineEntry.id == entry_id)
    )
    entry = result.scalars().first()
    
    if not entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Timeline entry not found"
        )
    
    # 更新字段
    update_data = {}
    if content is not None:
        # 验证内容不能为空
        if content.strip() == "":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="时间线内容不能为空"
            )
        update_data['content'] = content.strip()
    if title is not None:
        update_data['title'] = title
    if location is not None:
        update_data['location'] = location
    if is_active is not None:
        update_data['is_active'] = is_active
    if is_featured is not None:
        update_data['is_featured'] = is_featured
    if show_on_homepage is not None:
        update_data['show_on_homepage'] = show_on_homepage
    
    if update_data:
        await db.execute(
            update(GalleryTimelineEntry)
            .where(GalleryTimelineEntry.id == entry_id)
            .values(**update_data)
        )
    
    # 更新图片关联
    if image_ids is not None:
        # 删除现有关联
        await db.execute(
            delete(gallery_timeline_images)
            .where(gallery_timeline_images.c.timeline_entry_id == entry_id)
        )
        
        # 添加新关联
        for image_id_str in image_ids:
            try:
                image_id = int(image_id_str)
                # 检查图片是否存在
                result = await db.execute(select(Image).where(Image.id == image_id))
                image = result.scalars().first()
                if image:
                    await db.execute(
                        gallery_timeline_images.insert().values(
                            timeline_entry_id=entry_id,
                            image_id=image_id,
                            sort_order=0
                        )
                    )
            except ValueError:
                continue
    
    await db.commit()
    
    # 重新获取包含图片的条目
    result = await db.execute(
        select(GalleryTimelineEntry)
        .options(selectinload(GalleryTimelineEntry.images))
        .where(GalleryTimelineEntry.id == entry_id)
    )
    updated_entry = result.scalars().first()
    
    return updated_entry

@router.delete("/timeline/{entry_id}")
async def delete_timeline_entry(
    entry_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除Gallery时间线条目"""
    # 检查条目是否存在
    result = await db.execute(
        select(GalleryTimelineEntry).where(GalleryTimelineEntry.id == entry_id)
    )
    entry = result.scalars().first()
    
    if not entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Timeline entry not found"
        )
    
    # 删除条目（关联的图片会通过CASCADE自动删除）
    await db.execute(
        delete(GalleryTimelineEntry).where(GalleryTimelineEntry.id == entry_id)
    )
    await db.commit()
    
    return {"message": "Timeline entry deleted successfully"}

# 公开API - 用于前端展示
@router.get("/public/timeline", response_model=List[GalleryTimelineEntryResponse])
async def get_public_timeline_entries(
    skip: int = 0,
    limit: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """获取公开的Gallery时间线条目（仅活跃状态）"""
    result = await db.execute(
        select(GalleryTimelineEntry)
        .options(selectinload(GalleryTimelineEntry.images))
        .where(GalleryTimelineEntry.is_active == True)
        .order_by(GalleryTimelineEntry.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    entries = result.scalars().all()
    return entries
