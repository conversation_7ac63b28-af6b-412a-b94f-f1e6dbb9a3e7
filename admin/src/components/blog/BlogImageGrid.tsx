import React, { useState } from 'react';
import { Row, Col, Card, Image, Input, Select, Pagination, Empty, Spin, Tag, Space, message } from 'antd';
import { CheckOutlined, EyeOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import axiosInstance from '../../api/axiosInstance';

const { Search } = Input;
const { Option } = Select;

interface BlogImageGridProps {
  onSelect?: (image: any) => void;
  selectedImages?: any[];
  onImagesChange?: (images: any[]) => void;
  maxSelection?: number;
}

interface ImageData {
  id: number;
  url: string;
  thumbnail_url?: string;
  display_name?: string;
  original_filename: string;
  alt?: string;
  width?: number;
  height?: number;
  category?: {
    id: number;
    name: string;
    color?: string;
  };

  file_size?: number;
}

const BlogImageGrid: React.FC<BlogImageGridProps> = ({
  onSelect,
  selectedImages = [],
  onImagesChange,
  maxSelection = 20
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);

  // 获取图片分类
  const { data: categories = [] } = useQuery({
    queryKey: ['image-categories'],
    queryFn: async () => {
      const response = await axiosInstance.get('/image-categories/tree');
      return response.data;
    }
  });

  // 获取图片列表
  const { data: imagesData, isLoading, error } = useQuery({
    queryKey: ['images', searchQuery, selectedCategory, currentPage, pageSize],
    queryFn: async () => {
      const response = await axiosInstance.post('/images/search', {
        query: searchQuery || undefined,
        category_id: selectedCategory,
        // 不过滤usage_type，显示所有图片，让用户可以选择任何图片用于博客
        page: currentPage,
        page_size: pageSize
      });
      return response.data;
    }
  });

  const images = imagesData?.items || [];
  const total = imagesData?.total || 0;



  const getImageUrl = (image: ImageData) => {
    const baseUrl = 'http://100.90.150.110:8000';
    const imageUrl = image.thumbnail_url || image.url;

    // 如果URL已经是完整的URL（包含http），直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 否则添加基础URL
    return `${baseUrl}${imageUrl}`;
  };

  const handleImageClick = (image: ImageData) => {
    if (onImagesChange) {
      // 多选模式
      const isSelected = selectedImages.some(img => img.id === image.id);
      let newSelectedImages;

      if (isSelected) {
        // 取消选择
        newSelectedImages = selectedImages.filter(img => img.id !== image.id);
      } else {
        // 添加选择
        if (selectedImages.length >= maxSelection) {
          message.warning(`最多只能选择 ${maxSelection} 张图片`);
          return;
        }
        newSelectedImages = [...selectedImages, image];
      }

      onImagesChange(newSelectedImages);
    } else if (onSelect) {
      // 单选模式
      onSelect(image);
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  return (
    <div>
      {/* 搜索和筛选 */}
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Search
              placeholder="搜索图片..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onSearch={setSearchQuery}
              allowClear
            />
          </Col>
          <Col span={12}>
            <Select
              placeholder="选择分类"
              style={{ width: '100%' }}
              value={selectedCategory}
              onChange={setSelectedCategory}
              allowClear
            >
              {categories.map((category: any) => (
                <Option key={category.id} value={category.id}>
                  <Space>
                    {category.icon && <span>{category.icon}</span>}
                    {category.name}
                    <Tag color={category.color || 'default'}>
                      {category.image_count || 0}
                    </Tag>
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>

      {/* 错误显示 */}
      {error && (
        <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: 6 }}>
          <p style={{ color: '#ff4d4f', margin: 0 }}>
            加载图片时出错: {error.message || '未知错误'}
          </p>
        </div>
      )}

      {/* 图片网格 */}
      <Spin spinning={isLoading}>
        {images.length > 0 ? (
          <>
            <Row gutter={[16, 16]}>
              {images.map((image: ImageData) => {
                const isSelected = onImagesChange
                  ? selectedImages.some(img => img.id === image.id)
                  : selectedImages.includes(image.id);

                return (
                  <Col key={image.id} xs={12} sm={8} md={6} lg={4}>
                    <Card
                      hoverable
                      size="small"
                      style={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        border: isSelected ? '2px solid #1890ff' : undefined,
                        backgroundColor: isSelected ? '#f0f8ff' : undefined
                      }}
                    cover={
                      <div style={{ position: 'relative', height: 120, overflow: 'hidden' }}>
                        <Image
                          src={getImageUrl(image)}
                          alt={image.display_name || image.original_filename}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                          preview={{
                            mask: (
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexDirection: 'column',
                                gap: 4
                              }}>
                                <EyeOutlined style={{ fontSize: 16 }} />
                                <span style={{ fontSize: 12 }}>预览</span>
                              </div>
                            )
                          }}
                          onClick={(e) => e.stopPropagation()}
                        />
                        {isSelected && (
                          <div style={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            backgroundColor: '#1890ff',
                            borderRadius: '50%',
                            width: 24,
                            height: 24,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white'
                          }}>
                            <CheckOutlined style={{ fontSize: 12 }} />
                          </div>
                        )}
                      </div>
                    }
                    onClick={() => handleImageClick(image)}
                  >
                    <Card.Meta
                      title={
                        <div style={{ 
                          fontSize: '12px', 
                          fontWeight: 'normal',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {image.display_name || image.original_filename}
                        </div>
                      }
                      description={
                        <div style={{ fontSize: '11px', color: '#999' }}>
                          <div>{formatFileSize(image.file_size)}</div>
                          {image.width && image.height && (
                            <div>{image.width} × {image.height}</div>
                          )}
                          {image.category && (
                            <Tag 
                              size="small" 
                              color={image.category.color || 'default'}
                              style={{ marginTop: 4 }}
                            >
                              {image.category.name}
                            </Tag>
                          )}
                        </div>
                      }
                    />
                  </Card>
                </Col>
                );
              })}
            </Row>

            {/* 分页 */}
            {total > pageSize && (
              <div style={{ textAlign: 'center', marginTop: 24 }}>
                <Pagination
                  current={currentPage}
                  total={total}
                  pageSize={pageSize}
                  onChange={setCurrentPage}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) => 
                    `第 ${range[0]}-${range[1]} 项，共 ${total} 项`
                  }
                />
              </div>
            )}
          </>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无图片"
            style={{ margin: '40px 0' }}
          />
        )}
      </Spin>

      {/* 多选模式下显示已选择的图片数量 */}
      {onImagesChange && selectedImages.length > 0 && (
        <div style={{
          marginTop: 16,
          padding: 12,
          backgroundColor: '#f0f8ff',
          borderRadius: 6,
          textAlign: 'center'
        }}>
          已选择 {selectedImages.length} 张图片 (最多 {maxSelection} 张)
        </div>
      )}
    </div>
  );
};

export default BlogImageGrid;
