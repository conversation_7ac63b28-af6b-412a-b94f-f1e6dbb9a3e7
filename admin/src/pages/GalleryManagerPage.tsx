import React, { useState } from 'react';
import {
  Card, Row, Col, Button, Table, Space, Modal, Form, Input, Select,
  message, Popconfirm, Tag, Switch, Tooltip, Tabs, Image, Empty,
  Typography, Divider, Badge, Statistic, Pagination, Spin
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined,
  SettingOutlined, PictureOutlined, StarOutlined, AppstoreOutlined,
  ClockCircleOutlined, EnvironmentOutlined, FileTextOutlined, HistoryOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../api/axiosInstance';
import BlogImageGrid from '../components/blog/BlogImageGrid';
import EnhancedMarkdownEditor from '../components/EnhancedMarkdownEditor';
import TemplateContentGenerator from '../components/TemplateContentGenerator';
import BlogVersionHistory from '../components/BlogVersionHistory';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 相册接口定义 - 整合Gallery功能
interface Album {
  id: number;
  title: string;
  description?: string;
  date: string;
  location?: string;
  category?: string;
  slug?: string;
  layout_type: string;
  is_featured: boolean;
  sort_order: number;
  cover_image?: any;
  images: any[];
  created_at: string;
  updated_at: string;
}

// 相册分类接口
interface AlbumCategory {
  id: number;
  name: string;
  description?: string;
  album_count: number;
}

// 时间线条目接口
interface TimelineEntry {
  id: number;
  title?: string;
  content: string;
  location?: string;
  is_active: boolean;
  is_featured: boolean;
  show_on_homepage: boolean;
  images: any[];
  created_at: string;
  updated_at: string;
}

const GalleryManagerPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('timeline');
  
  // 时间线相关状态
  const [timelineModalVisible, setTimelineModalVisible] = useState(false);
  const [editingTimeline, setEditingTimeline] = useState<TimelineEntry | null>(null);
  const [timelineContent, setTimelineContent] = useState<string>('');
  const [templateGeneratorVisible, setTemplateGeneratorVisible] = useState<boolean>(false);
  const [versionHistoryVisible, setVersionHistoryVisible] = useState<boolean>(false);
  
  // 相册相关状态
  const [albumModalVisible, setAlbumModalVisible] = useState(false);
  const [editingAlbum, setEditingAlbum] = useState<Album | null>(null);
  const [selectedImages, setSelectedImages] = useState<number[]>([]);
  const [selectedImageData, setSelectedImageData] = useState<any[]>([]);
  
  // 通用状态
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);

  const queryClient = useQueryClient();

  // 获取图片URL的函数
  const getImageUrl = (image: any) => {
    const baseUrl = 'http://**************:8000';
    const imageUrl = image.thumbnail_url || image.url;

    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }
    return `${baseUrl}${imageUrl}`;
  };

  // 时间线模板数据
  const timelineTemplates = [
    {
      id: 'timeline-daily',
      name: '日常记录',
      description: '记录日常生活的点点滴滴',
      content: `## 今日记录

**时间**: {date}
**地点**: {location}

### 主要内容
{content}

### 心情感受
今天的心情：😊

### 图片记录
{images}

---
*记录于 {timestamp}*`
    },
    {
      id: 'timeline-travel',
      name: '旅行记录',
      description: '记录旅行中的美好时光',
      content: `## 🌍 旅行记录

**目的地**: {location}
**日期**: {date}

### 🎯 今日行程
{content}

### 📸 精彩瞬间
{images}

### 💭 旅行感悟
这次旅行让我感受到...

---
*旅行记录 · {timestamp}*`
    },
    {
      id: 'timeline-work',
      name: '工作记录',
      description: '记录工作中的重要时刻',
      content: `## 💼 工作记录

**日期**: {date}
**项目**: {title}

### 📋 工作内容
{content}

### 🎯 完成情况
- [ ] 任务1
- [ ] 任务2
- [ ] 任务3

### 📊 相关资料
{images}

---
*工作记录 · {timestamp}*`
    }
  ];

  // ==================== API查询 ====================

  // 获取时间线数据
  const { data: timelineData, isLoading: timelineLoading } = useQuery({
    queryKey: ['gallery-timeline', currentPage, pageSize],
    queryFn: async () => {
      const skip = (currentPage - 1) * pageSize;
      const params = new URLSearchParams({
        skip: skip.toString(),
        limit: pageSize.toString()
      });
      const response = await axiosInstance.get(`/gallery/timeline?${params}`);
      return response.data;
    }
  });

  // 获取相册数据
  const { data: albumsData, isLoading: albumsLoading } = useQuery({
    queryKey: ['albums', currentPage, pageSize, searchKeyword],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        size: pageSize.toString(),
        ...(searchKeyword && { search: searchKeyword })
      });
      const response = await axiosInstance.get(`/images/albums?${params}`);
      return response.data;
    }
  });

  // 获取相册分类
  const { data: categoriesData } = useQuery({
    queryKey: ['album-categories'],
    queryFn: async () => {
      const response = await axiosInstance.get('/images/albums/categories');
      return response.data;
    }
  });

  // 获取图片数据
  const { data: imagesData } = useQuery({
    queryKey: ['images'],
    queryFn: async () => {
      const response = await axiosInstance.get('/images/images?page_size=1000');
      return response.data;
    }
  });

  // ==================== 时间线操作 ====================

  // 时间线创建/更新
  const timelineMutation = useMutation({
    mutationFn: async (data: any) => {
      if (editingTimeline) {
        return axiosInstance.put(`/gallery/timeline/${editingTimeline.id}`, data);
      } else {
        return axiosInstance.post('/gallery/timeline', data);
      }
    },
    onSuccess: () => {
      message.success(editingTimeline ? '时间线条目更新成功' : '时间线条目创建成功');
      setTimelineModalVisible(false);
      setEditingTimeline(null);
      setTimelineContent('');
      setSelectedImages([]);
      setSelectedImageData([]);
      queryClient.invalidateQueries({ queryKey: ['gallery-timeline'] });
    },
    onError: (error: any) => {
      message.error(error.response?.data?.detail || '操作失败');
    }
  });

  // 时间线删除
  const deleteTimelineMutation = useMutation({
    mutationFn: async (id: number) => {
      return axiosInstance.delete(`/gallery/timeline/${id}`);
    },
    onSuccess: () => {
      message.success('时间线条目删除成功');
      queryClient.invalidateQueries({ queryKey: ['gallery-timeline'] });
    },
    onError: (error: any) => {
      message.error(error.response?.data?.detail || '删除失败');
    }
  });

  // ==================== 相册操作 ====================

  // 相册创建/更新
  const albumMutation = useMutation({
    mutationFn: async (data: FormData) => {
      if (editingAlbum) {
        return axiosInstance.put(`/images/albums/${editingAlbum.id}`, data, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
      } else {
        return axiosInstance.post('/images/albums', data, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
      }
    },
    onSuccess: () => {
      message.success(editingAlbum ? '相册更新成功' : '相册创建成功');
      setAlbumModalVisible(false);
      setEditingAlbum(null);
      setSelectedImages([]);
      setSelectedImageData([]);
      queryClient.invalidateQueries({ queryKey: ['albums'] });
      queryClient.invalidateQueries({ queryKey: ['album-categories'] });
    },
    onError: (error: any) => {
      message.error(error.response?.data?.detail || '操作失败');
    }
  });

  // 相册删除
  const deleteAlbumMutation = useMutation({
    mutationFn: async (id: number) => {
      return axiosInstance.delete(`/images/albums/${id}`);
    },
    onSuccess: () => {
      message.success('相册删除成功');
      queryClient.invalidateQueries({ queryKey: ['albums'] });
      queryClient.invalidateQueries({ queryKey: ['album-categories'] });
    },
    onError: (error: any) => {
      message.error(error.response?.data?.detail || '删除失败');
    }
  });

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>Gallery 管理</Title>
      <Text type="secondary">管理时间线内容和相册集合</Text>

      <Tabs activeKey={activeTab} onChange={setActiveTab} style={{ marginTop: '24px' }}>
        {/* 时间线管理 Tab */}
        <TabPane tab="时间线管理" key="timeline" icon={<ClockCircleOutlined />}>
          <Card>
            <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
              <Title level={4}>时间线条目</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingTimeline(null);
                  setTimelineContent('');
                  setSelectedImages([]);
                  setSelectedImageData([]);
                  setTimelineModalVisible(true);
                }}
              >
                新建时间线
              </Button>
            </div>

            {/* 搜索和筛选 */}
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={8}>
                <Input.Search
                  placeholder="搜索时间线内容..."
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onSearch={() => setCurrentPage(1)}
                  allowClear
                />
              </Col>
            </Row>

            {/* 时间线列表 */}
            <Table
              dataSource={timelineData || []}
              loading={timelineLoading}
              rowKey="id"
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: (timelineData || []).length,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size || 20);
                },
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
              columns={[
                {
                  title: '标题',
                  dataIndex: 'title',
                  key: 'title',
                  width: 150,
                  render: (title: string) => title || <Text type="secondary">无标题</Text>
                },
                {
                  title: '内容预览',
                  dataIndex: 'content',
                  key: 'content',
                  render: (content: string) => (
                    <div style={{ maxWidth: '300px' }}>
                      <Text ellipsis={{ tooltip: content }}>
                        {content.replace(/[#*`]/g, '').substring(0, 100)}
                      </Text>
                    </div>
                  )
                },
                {
                  title: '位置',
                  dataIndex: 'location',
                  key: 'location',
                  width: 120,
                  render: (location: string) => location ? (
                    <Tag icon={<EnvironmentOutlined />} color="blue">{location}</Tag>
                  ) : '-'
                },
                {
                  title: '图片数量',
                  key: 'imageCount',
                  width: 100,
                  render: (record: TimelineEntry) => (
                    <Badge count={record.images?.length || 0} showZero color="green" />
                  )
                },
                {
                  title: '状态',
                  key: 'status',
                  width: 120,
                  render: (record: TimelineEntry) => (
                    <Space>
                      {record.is_featured && <Tag color="gold">精选</Tag>}
                      {record.show_on_homepage && <Tag color="blue">首页</Tag>}
                      <Tag color={record.is_active ? 'green' : 'red'}>
                        {record.is_active ? '启用' : '禁用'}
                      </Tag>
                    </Space>
                  )
                },
                {
                  title: '创建时间',
                  dataIndex: 'created_at',
                  key: 'created_at',
                  width: 180,
                  render: (date: string) => new Date(date).toLocaleString()
                },
                {
                  title: '操作',
                  key: 'actions',
                  width: 200,
                  render: (record: TimelineEntry) => (
                    <Space>
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => {
                            setEditingTimeline(record);
                            setTimelineContent(record.content);
                            setSelectedImages(record.images?.map(img => img.id) || []);
                            setSelectedImageData(record.images || []);
                            setTimelineModalVisible(true);
                          }}
                        />
                      </Tooltip>
                      <Tooltip title="版本历史">
                        <Button
                          type="text"
                          icon={<HistoryOutlined />}
                          onClick={() => {
                            setEditingTimeline(record);
                            setTimelineContent(record.content);
                            setVersionHistoryVisible(true);
                          }}
                        />
                      </Tooltip>
                      <Popconfirm
                        title="确定删除这个时间线条目吗？"
                        onConfirm={() => deleteTimelineMutation.mutate(record.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Tooltip title="删除">
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                          />
                        </Tooltip>
                      </Popconfirm>
                    </Space>
                  )
                }
              ]}
            />
          </Card>
        </TabPane>

        {/* 相册管理 Tab */}
        <TabPane tab="相册管理" key="albums" icon={<FolderOpenOutlined />}>
          <Card>
            <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
              <Title level={4}>相册列表</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingAlbum(null);
                  setSelectedImages([]);
                  setSelectedImageData([]);
                  setAlbumModalVisible(true);
                }}
              >
                新建相册
              </Button>
            </div>

            {/* 分类统计 */}
            {categoriesData && categoriesData.length > 0 && (
              <Row gutter={16} style={{ marginBottom: '16px' }}>
                <Col span={24}>
                  <Card size="small">
                    <Title level={5}>分类统计</Title>
                    <Row gutter={16}>
                      {categoriesData.map((category: AlbumCategory) => (
                        <Col key={category.id} span={6}>
                          <Statistic
                            title={category.name}
                            value={category.album_count}
                            suffix="个相册"
                          />
                        </Col>
                      ))}
                    </Row>
                  </Card>
                </Col>
              </Row>
            )}

            {/* 搜索和筛选 */}
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={8}>
                <Input.Search
                  placeholder="搜索相册标题..."
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onSearch={() => setCurrentPage(1)}
                  allowClear
                />
              </Col>
            </Row>

            {/* 相册列表 */}
            <Table
              dataSource={albumsData || []}
              loading={albumsLoading}
              rowKey="id"
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: albumsData?.length || 0,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size || 20);
                },
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
              columns={[
                {
                  title: '封面',
                  key: 'cover',
                  width: 80,
                  render: (record: Album) => (
                    record.cover_image ? (
                      <Image
                        src={getImageUrl(record.cover_image)}
                        alt={record.title}
                        width={60}
                        height={60}
                        style={{ objectFit: 'cover', borderRadius: '4px' }}
                        fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                      />
                    ) : (
                      <div style={{
                        width: 60,
                        height: 60,
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <PictureOutlined style={{ color: '#ccc' }} />
                      </div>
                    )
                  )
                },
                {
                  title: '标题',
                  dataIndex: 'title',
                  key: 'title',
                  render: (title: string, record: Album) => (
                    <div>
                      <Text strong>{title}</Text>
                      {record.description && (
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {record.description.substring(0, 50)}
                            {record.description.length > 50 ? '...' : ''}
                          </Text>
                        </div>
                      )}
                    </div>
                  )
                },
                {
                  title: '分类',
                  dataIndex: 'category',
                  key: 'category',
                  width: 120,
                  render: (category: string) => category ? (
                    <Tag color="blue">{category}</Tag>
                  ) : '-'
                },
                {
                  title: '布局',
                  dataIndex: 'layout_type',
                  key: 'layout_type',
                  width: 100,
                  render: (layout: string) => (
                    <Tag color={layout === 'grid' ? 'green' : layout === 'masonry' ? 'orange' : 'purple'}>
                      {layout}
                    </Tag>
                  )
                },
                {
                  title: '图片数量',
                  key: 'imageCount',
                  width: 100,
                  render: (record: Album) => (
                    <Badge count={record.images?.length || 0} showZero color="green" />
                  )
                },
                {
                  title: '状态',
                  key: 'status',
                  width: 120,
                  render: (record: Album) => (
                    <Space>
                      {record.is_featured && <Tag color="gold">精选</Tag>}
                      <Text type="secondary">排序: {record.sort_order}</Text>
                    </Space>
                  )
                },
                {
                  title: '日期',
                  dataIndex: 'date',
                  key: 'date',
                  width: 120,
                  render: (date: string) => new Date(date).toLocaleDateString()
                },
                {
                  title: '操作',
                  key: 'actions',
                  width: 150,
                  render: (record: Album) => (
                    <Space>
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => {
                            setEditingAlbum(record);
                            setSelectedImages(record.images?.map(img => img.id) || []);
                            setSelectedImageData(record.images || []);
                            setAlbumModalVisible(true);
                          }}
                        />
                      </Tooltip>
                      <Popconfirm
                        title="确定删除这个相册吗？"
                        onConfirm={() => deleteAlbumMutation.mutate(record.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Tooltip title="删除">
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                          />
                        </Tooltip>
                      </Popconfirm>
                    </Space>
                  )
                }
              ]}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 时间线编辑模态框 */}
      <Modal
        title={editingTimeline ? '编辑时间线' : '新建时间线'}
        open={timelineModalVisible}
        onCancel={() => {
          setTimelineModalVisible(false);
          setEditingTimeline(null);
          setTimelineContent('');
          setSelectedImages([]);
          setSelectedImageData([]);
        }}
        width={1000}
        footer={null}
      >
        <Form
          layout="vertical"
          onFinish={(values) => {
            // 验证内容不能为空
            if (!timelineContent || timelineContent.trim() === '') {
              message.error('请输入时间线内容');
              return;
            }

            const formData = new FormData();
            formData.append('content', timelineContent.trim());
            if (values.title) formData.append('title', values.title);
            if (values.location) formData.append('location', values.location);
            formData.append('is_active', values.is_active ? 'true' : 'false');
            formData.append('is_featured', values.is_featured ? 'true' : 'false');
            formData.append('show_on_homepage', values.show_on_homepage ? 'true' : 'false');

            selectedImages.forEach(imageId => {
              formData.append('image_ids', imageId.toString());
            });

            timelineMutation.mutate(formData);
          }}
          initialValues={{
            title: editingTimeline?.title || '',
            location: editingTimeline?.location || '',
            is_active: editingTimeline?.is_active ?? true,
            is_featured: editingTimeline?.is_featured ?? false,
            show_on_homepage: editingTimeline?.show_on_homepage ?? false
          }}
        >
          <Form.Item label="标题" name="title">
            <Input placeholder="请输入时间线标题（可选）" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="位置" name="location">
                <Input placeholder="请输入位置信息" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="状态设置">
                <Space>
                  <Form.Item name="is_active" valuePropName="checked" style={{ marginBottom: 0 }}>
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                  </Form.Item>
                  <Form.Item name="is_featured" valuePropName="checked" style={{ marginBottom: 0 }}>
                    <Switch checkedChildren="精选" unCheckedChildren="普通" />
                  </Form.Item>
                  <Form.Item name="show_on_homepage" valuePropName="checked" style={{ marginBottom: 0 }}>
                    <Switch checkedChildren="首页显示" unCheckedChildren="不显示" />
                  </Form.Item>
                </Space>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="内容">
            <div style={{ marginBottom: '8px' }}>
              <Space>
                <Button
                  type="default"
                  icon={<FileTextOutlined />}
                  onClick={() => setTemplateGeneratorVisible(true)}
                >
                  选择模板
                </Button>
                {editingTimeline && (
                  <Button
                    type="default"
                    icon={<HistoryOutlined />}
                    onClick={() => setVersionHistoryVisible(true)}
                  >
                    版本历史
                  </Button>
                )}
              </Space>
            </div>
            <EnhancedMarkdownEditor
              value={timelineContent}
              onChange={setTimelineContent}
              height={300}
              placeholder="请输入时间线内容..."
            />
          </Form.Item>

          <Form.Item label="关联图片">
            <BlogImageGrid
              selectedImages={selectedImageData}
              onImagesChange={(images) => {
                setSelectedImageData(images);
                setSelectedImages(images.map(img => img.id));
              }}
              maxSelection={20}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setTimelineModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={timelineMutation.isPending}
              >
                {editingTimeline ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 相册编辑模态框 */}
      <Modal
        title={editingAlbum ? '编辑相册' : '新建相册'}
        open={albumModalVisible}
        onCancel={() => {
          setAlbumModalVisible(false);
          setEditingAlbum(null);
          setSelectedImages([]);
          setSelectedImageData([]);
        }}
        width={800}
        footer={null}
      >
        <Form
          layout="vertical"
          onFinish={(values) => {
            const formData = new FormData();
            formData.append('title', values.title);
            if (values.description) formData.append('description', values.description);
            formData.append('date', values.date);
            if (values.location) formData.append('location', values.location);
            if (values.category) formData.append('category', values.category);
            if (values.slug) formData.append('slug', values.slug);
            formData.append('layout_type', values.layout_type);
            formData.append('is_featured', values.is_featured ? 'true' : 'false');
            formData.append('sort_order', values.sort_order.toString());

            selectedImages.forEach(imageId => {
              formData.append('image_ids', imageId.toString());
            });

            albumMutation.mutate(formData);
          }}
          initialValues={{
            title: editingAlbum?.title || '',
            description: editingAlbum?.description || '',
            date: editingAlbum?.date || new Date().toISOString().split('T')[0],
            location: editingAlbum?.location || '',
            category: editingAlbum?.category || '',
            slug: editingAlbum?.slug || '',
            layout_type: editingAlbum?.layout_type || 'grid',
            is_featured: editingAlbum?.is_featured ?? false,
            sort_order: editingAlbum?.sort_order ?? 0
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="相册标题"
                name="title"
                rules={[{ required: true, message: '请输入相册标题' }]}
              >
                <Input placeholder="请输入相册标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="日期"
                name="date"
                rules={[{ required: true, message: '请选择日期' }]}
              >
                <Input type="date" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="描述" name="description">
            <Input.TextArea rows={3} placeholder="请输入相册描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="分类" name="category">
                <Input placeholder="请输入分类名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="URL标识" name="slug">
                <Input placeholder="用于URL的友好标识" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="位置" name="location">
                <Input placeholder="请输入位置信息" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="布局类型" name="layout_type">
                <Select>
                  <Option value="grid">网格布局</Option>
                  <Option value="masonry">瀑布流</Option>
                  <Option value="carousel">轮播图</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="排序顺序" name="sort_order">
                <Input type="number" placeholder="数字越小越靠前" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="精选" name="is_featured" valuePropName="checked">
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="相册图片">
            <BlogImageGrid
              selectedImages={selectedImageData}
              onImagesChange={(images) => {
                setSelectedImageData(images);
                setSelectedImages(images.map(img => img.id));
              }}
              maxSelection={100}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setAlbumModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={albumMutation.isPending}
              >
                {editingAlbum ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 模板选择器 */}
      <TemplateContentGenerator
        visible={templateGeneratorVisible}
        templates={timelineTemplates}
        onCancel={() => setTemplateGeneratorVisible(false)}
        onApply={(content) => {
          setTimelineContent(content);
          setTemplateGeneratorVisible(false);
        }}
        contentType="timeline"
      />

      {/* 版本历史 */}
      {editingTimeline && (
        <BlogVersionHistory
          visible={versionHistoryVisible}
          onClose={() => setVersionHistoryVisible(false)}
          blogSlug={editingTimeline.id.toString()}
          blogTitle={editingTimeline.title || '时间线条目'}
          currentContent={timelineContent}
          onBlogUpdated={() => {
            queryClient.invalidateQueries({ queryKey: ['gallery-timeline'] });
          }}
        />
      )}
    </div>
  );
};

export default GalleryManagerPage;
